/**
 * WebSocket管理模块
 * 负责处理与服务器的WebSocket连接和实时通信
 */

class WebSocketManager {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // 1秒
        this.heartbeatInterval = null;
        this.currentChatId = null;
        this.typingTimeout = null;
        
        // 事件回调
        this.callbacks = {
            onConnect: [],
            onDisconnect: [],
            onNewMessage: [],
            onMessagesRead: [],
            onUserOnline: [],
            onUserOffline: [],
            onUserTyping: [],
            group_message: [], // 添加群聊消息事件
            group_messages_read: [], // 添加群聊消息已读事件
            group_member_change: [] // 添加群成员变化事件
        };
    }

    /**
     * 初始化WebSocket连接
     */
    init() {
        if (this.socket) {
            this.disconnect();
        }

        try {
            // 显示连接状态
            this.updateStatusIndicator('connecting');

            // 创建Socket.IO连接
            this.socket = io({
                transports: ['websocket', 'polling'],
                timeout: 20000,
                forceNew: true
            });

            this.setupEventListeners();
            console.log('WebSocket管理器已初始化');
        } catch (error) {
            console.error('WebSocket初始化失败:', error);
            this.updateStatusIndicator('disconnected');
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 连接成功
        this.socket.on('connect', () => {
            console.log('🔥 WebSocket已连接');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            this.updateStatusIndicator('connected');
            this.triggerCallbacks('onConnect');

            // 🔥 重连后重新初始化全局监听器
            if (typeof window.initGlobalWebSocketListeners === 'function') {
                console.log('🔥 WebSocket重连后重新初始化全局监听器');
                // 重置初始化标记，允许重新初始化
                if (window.wsManager) {
                    window.wsManager._globalListenersInitialized = false;
                }
                setTimeout(() => {
                    window.initGlobalWebSocketListeners();
                }, 100);
            }
        });

        // 连接断开
        this.socket.on('disconnect', (reason) => {
            console.log('WebSocket连接断开:', reason);
            this.isConnected = false;
            this.stopHeartbeat();
            this.updateStatusIndicator('disconnected');
            this.triggerCallbacks('onDisconnect', reason);

            // 自动重连
            if (reason !== 'io client disconnect') {
                this.attemptReconnect();
            }
        });

        // 连接错误
        this.socket.on('connect_error', (error) => {
            console.error('WebSocket连接错误:', error);
            this.updateStatusIndicator('connecting');
            this.attemptReconnect();
        });

        // 新消息
        this.socket.on('new_message', (data) => {
            console.log('收到新消息:', data);
            this.triggerCallbacks('onNewMessage', data);
        });

        // 消息已读
        this.socket.on('messages_read', (data) => {
            console.log('消息已读通知:', data);
            this.triggerCallbacks('onMessagesRead', data);
        });

        // 群聊消息
        this.socket.on('group_message', (data) => {
            console.log('收到群聊消息:', data);
            this.triggerCallbacks('group_message', data);
        });

        // 群聊消息已读
        this.socket.on('group_messages_read', (data) => {
            this.triggerCallbacks('group_messages_read', data);
        });

        // 群成员变化
        this.socket.on('group_member_change', (data) => {
            console.log('收到群成员变化通知:', data);
            this.triggerCallbacks('group_member_change', data);
        });

        // 用户上线
        this.socket.on('user_online', (data) => {
            this.triggerCallbacks('onUserOnline', data);
        });

        // 用户下线
        this.socket.on('user_offline', (data) => {
            this.triggerCallbacks('onUserOffline', data);
        });

        // 用户正在输入
        this.socket.on('user_typing', (data) => {
            console.log('用户输入状态:', data);
            this.triggerCallbacks('onUserTyping', data);
        });

        // 心跳响应
        this.socket.on('heartbeat_response', (data) => {
            // console.log('心跳响应:', data);
        });
    }

    /**
     * 断开连接
     */
    disconnect() {
        if (this.socket) {
            // 主动断开连接，确保服务器收到断开事件
            this.socket.disconnect();
            this.socket = null;
        }
        this.isConnected = false;
        this.stopHeartbeat();
    }

    /**
     * 尝试重连
     */
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('WebSocket重连次数已达上限');
            return;
        }

        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`WebSocket重连尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts}，${delay}ms后重试`);
        
        setTimeout(() => {
            if (!this.isConnected) {
                this.init();
            }
        }, delay);
    }

    /**
     * 开始心跳检测
     */
    startHeartbeat() {
        this.stopHeartbeat();
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.socket) {
                this.socket.emit('heartbeat');
            }
        }, 30000); // 30秒心跳
    }

    /**
     * 停止心跳检测
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 加入聊天房间
     */
    joinChat(chatWithId) {
        if (this.isConnected && this.socket) {
            this.currentChatId = chatWithId;
            this.socket.emit('join_chat', { chat_with_id: chatWithId });
            console.log('加入聊天房间:', chatWithId);
        }
    }

    /**
     * 离开聊天房间
     */
    leaveChat(chatWithId) {
        if (this.isConnected && this.socket) {
            this.socket.emit('leave_chat', { chat_with_id: chatWithId });
            console.log('离开聊天房间:', chatWithId);
            if (this.currentChatId === chatWithId) {
                this.currentChatId = null;
            }
        }
    }

    /**
     * 标记消息为已读
     */
    markMessagesAsRead(messageIds, senderId) {
        if (this.isConnected && this.socket && messageIds.length > 0) {
            this.socket.emit('message_read', {
                message_ids: messageIds,
                sender_id: senderId
            });
            console.log('标记消息已读:', messageIds);
        }
    }

    /**
     * 发送正在输入状态
     */
    sendTypingStart(chatWithId) {
        if (this.isConnected && this.socket) {
            this.socket.emit('typing_start', { chat_with_id: chatWithId });
        }
    }

    /**
     * 发送停止输入状态
     */
    sendTypingStop(chatWithId) {
        if (this.isConnected && this.socket) {
            this.socket.emit('typing_stop', { chat_with_id: chatWithId });
        }
    }

    /**
     * 处理输入状态（防抖）
     */
    handleTyping(chatWithId, isTyping) {
        if (isTyping) {
            this.sendTypingStart(chatWithId);
            
            // 清除之前的定时器
            if (this.typingTimeout) {
                clearTimeout(this.typingTimeout);
            }
            
            // 3秒后自动发送停止输入
            this.typingTimeout = setTimeout(() => {
                this.sendTypingStop(chatWithId);
            }, 3000);
        } else {
            this.sendTypingStop(chatWithId);
            if (this.typingTimeout) {
                clearTimeout(this.typingTimeout);
                this.typingTimeout = null;
            }
        }
    }

    /**
     * 注册事件回调
     */
    on(event, callback) {
        if (this.callbacks[event]) {
            this.callbacks[event].push(callback);
        }
    }

    /**
     * 移除事件回调
     */
    off(event, callback) {
        if (this.callbacks[event]) {
            const index = this.callbacks[event].indexOf(callback);
            if (index > -1) {
                this.callbacks[event].splice(index, 1);
            }
        }
    }

    /**
     * 触发回调函数
     */
    triggerCallbacks(event, data) {
        if (this.callbacks[event]) {
            this.callbacks[event].forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`WebSocket回调执行错误 (${event}):`, error);
                }
            });
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            currentChatId: this.currentChatId
        };
    }

    /**
     * 更新状态指示器
     */
    updateStatusIndicator(status) {
        const indicator = document.getElementById('websocket-status');
        if (!indicator) return;

        const icon = indicator.querySelector('i');
        const text = indicator.querySelector('span');

        // 移除所有状态类
        indicator.className = 'websocket-status';

        switch (status) {
            case 'connected':
                indicator.classList.add('connected');
                if (icon) icon.className = 'fas fa-wifi';
                if (text) text.textContent = '已连接';
                // 3秒后隐藏指示器
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 3000);
                break;
            case 'disconnected':
                indicator.classList.add('disconnected');
                if (icon) icon.className = 'fas fa-wifi-slash';
                if (text) text.textContent = '连接断开';
                indicator.style.display = 'block';
                break;
            case 'connecting':
                indicator.classList.add('connecting');
                if (icon) icon.className = 'fas fa-spinner fa-spin';
                if (text) text.textContent = '重连中...';
                indicator.style.display = 'block';
                break;
        }
    }
}

// 创建全局WebSocket管理器实例
window.wsManager = new WebSocketManager();

// 在页面加载完成后初始化WebSocket（如果用户已登录）
document.addEventListener('DOMContentLoaded', () => {
    // 检查用户是否已登录，如果已登录则初始化WebSocket
    // 这个检查会在common.js中的用户状态检查后进行
    setTimeout(() => {
        // 检查localStorage中的用户信息，而不是window.currentUser
        let user = null;
        try {
            user = JSON.parse(localStorage.getItem('wechat_user'));
        } catch(e) {}

        if (user && user.id) {
            window.wsManager.init();
        }
    }, 1000);
});

// 添加全局错误处理
window.addEventListener('error', (event) => {
    if (event.message && event.message.includes('socket')) {
        console.error('WebSocket相关错误:', event.error);
    }
});

// 添加调试功能
window.wsDebug = {
    getStatus: () => window.wsManager.getConnectionStatus(),
    reconnect: () => window.wsManager.init(),
    disconnect: () => window.wsManager.disconnect(),
    sendTest: (chatId) => {
        if (window.wsManager.isConnected) {
            window.wsManager.joinChat(chatId);
            console.log('已加入测试聊天房间:', chatId);
        } else {
            console.log('WebSocket未连接');
        }
    }
};
