// 存储朋友圈数据
let moments = [];

// 当前正在评论的朋友圈ID
let currentCommentMomentId = null;
// 当前正在回复的评论ID
let currentReplyCommentId = null;

// 下拉刷新实例
let momentsPullToRefresh = null;

// 上传的媒体文件
let uploadedMedia = {
    images: [],
    video: null
};

// 初始化朋友圈页面
function initMoments() {
    // 检查是否已登录
    const wechatUser = JSON.parse(localStorage.getItem('wechat_user') || '{}');
    if (!wechatUser || !wechatUser.username) {
        // 不在这里调用showLoginModal，因为common.js已经处理了登录状态检查
        return;
    }

    // 设置用户信息
    updateUserInfo();

    // 设置封面 - 使用setTimeout确保DOM元素已经准备好
    setTimeout(() => {
        updateCover();
    }, 100);

    // 获取朋友圈列表
    fetchMoments();

    // 确保评论框和封面选择框默认隐藏
    hideCommentModal();
    hideCoverModal();

    // 清理并重新绑定事件监听器（防止重复绑定）
    setupMomentsEventListeners();

    // 初始化朋友圈删除确认弹窗
    initMomentsDeleteModal();

    // 初始化下拉刷新功能
    initMomentsPullToRefresh();

    // 添加事件委托监听器处理回复按钮点击
    const momentsContainer = document.getElementById('moments-container');
    if (momentsContainer) {
        momentsContainer.addEventListener('click', function(event) {
            // 检查点击的是否是回复按钮
            if (event.target.closest('.comment-reply-btn')) {
                const replyBtn = event.target.closest('.comment-reply-btn');
                const momentId = parseInt(replyBtn.dataset.momentId);
                const commentId = parseInt(replyBtn.dataset.commentId);
                const userName = replyBtn.dataset.userName;



                if (momentId && commentId && userName) {
                    showReplyModal(momentId, commentId, userName);
                }
            }
        });
    }
    
    // 其他事件绑定已移至setupMomentsEventListeners函数中
    // 表情选择器事件绑定也在setupMomentsEventListeners中处理

}

// 设置朋友圈事件监听器（防重复绑定）
function setupMomentsEventListeners() {
    console.log('setupMomentsEventListeners 被调用');

    // 清理并重新绑定发布按钮事件
    const publishBtn = document.getElementById('publish-btn');
    if (publishBtn) {
        // 移除旧的事件监听器
        publishBtn.replaceWith(publishBtn.cloneNode(true));
        const newPublishBtn = document.getElementById('publish-btn');
        newPublishBtn.addEventListener('click', publishMoment);

    }

    // 清理并重新绑定图片上传事件
    const imageUpload = document.getElementById('image-upload');
    if (imageUpload) {
        imageUpload.replaceWith(imageUpload.cloneNode(true));
        const newImageUpload = document.getElementById('image-upload');
        newImageUpload.addEventListener('change', handleImageUpload);

    }

    // 清理并重新绑定视频上传事件
    const videoUpload = document.getElementById('video-upload');
    if (videoUpload) {
        videoUpload.replaceWith(videoUpload.cloneNode(true));
        const newVideoUpload = document.getElementById('video-upload');
        newVideoUpload.addEventListener('change', handleVideoUpload);

    }

    // 清理并重新绑定封面相关事件
    const coverWrapper = document.getElementById('cover-wrapper');
    if (coverWrapper) {
        coverWrapper.replaceWith(coverWrapper.cloneNode(true));
        // 注意：不在这里添加点击事件，由 toggleCoverEditability 函数控制
    }

    const closeCover = document.getElementById('close-cover');
    if (closeCover) {
        closeCover.replaceWith(closeCover.cloneNode(true));
        const newCloseCover = document.getElementById('close-cover');
        newCloseCover.addEventListener('click', hideCoverModal);

    }

    const coverImageUpload = document.getElementById('cover-image-upload');
    if (coverImageUpload) {
        coverImageUpload.replaceWith(coverImageUpload.cloneNode(true));
        const newCoverImageUpload = document.getElementById('cover-image-upload');
        newCoverImageUpload.addEventListener('change', handleCoverImageUpload);

    }

    const coverVideoUpload = document.getElementById('cover-video-upload');
    if (coverVideoUpload) {
        coverVideoUpload.replaceWith(coverVideoUpload.cloneNode(true));
        const newCoverVideoUpload = document.getElementById('cover-video-upload');
        newCoverVideoUpload.addEventListener('change', handleCoverVideoUpload);

    }

    // 清理并重新绑定表情按钮事件
    const emojiBtn = document.getElementById('post-emoji-btn');
    const emojiPicker = document.getElementById('post-emoji-picker');
    const postContent = document.getElementById('post-content');

    if (emojiBtn) {
        emojiBtn.replaceWith(emojiBtn.cloneNode(true));
        const newEmojiBtn = document.getElementById('post-emoji-btn');
        newEmojiBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('表情按钮被点击');
            toggleEmojiPicker('post-emoji-picker', e);
        });
        console.log('✅ 表情按钮事件监听器已绑定');
    }

    // 重新绑定表情选择事件
    if (emojiPicker && postContent) {
        const emojis = emojiPicker.querySelectorAll('.emoji');
        emojis.forEach(emoji => {
            emoji.replaceWith(emoji.cloneNode(true));
        });

        const newEmojis = emojiPicker.querySelectorAll('.emoji');
        newEmojis.forEach(emoji => {
            emoji.addEventListener('click', function(e) {
                e.stopPropagation();
                const emojiChar = this.textContent;

                // 获取当前光标位置
                const startPos = postContent.selectionStart;
                const endPos = postContent.selectionEnd;

                // 插入表情
                const textBefore = postContent.value.substring(0, startPos);
                const textAfter = postContent.value.substring(endPos);
                postContent.value = textBefore + emojiChar + textAfter;

                // 设置光标位置到表情后面
                const newCursorPos = startPos + emojiChar.length;
                postContent.setSelectionRange(newCursorPos, newCursorPos);

                // 隐藏表情选择器
                emojiPicker.classList.remove('active');

                // 触发input事件
                postContent.dispatchEvent(new Event('input', { bubbles: true }));

                console.log(`表情 ${emojiChar} 已插入`);
            });
        });

    }

    // 清理并重新绑定评论相关事件
    const cancelReply = document.getElementById('moment-cancel-reply');
    if (cancelReply) {
        cancelReply.replaceWith(cancelReply.cloneNode(true));
        const newCancelReply = document.getElementById('moment-cancel-reply');
        newCancelReply.addEventListener('click', cancelReplyHandler);

    }

    // 绑定模态框点击事件（这些不需要清理，因为是每次重新绑定）
    const commentModal = document.getElementById('moment-comment-modal');
    const coverModal = document.getElementById('cover-modal');

    if (commentModal) {
        commentModal.addEventListener('click', (e) => {
            if (e.target === commentModal) {
                hideCommentModal();
            }
        });

    }

    if (coverModal) {
        coverModal.addEventListener('click', (e) => {
            if (e.target === coverModal) {
                hideCoverModal();
            }
        });
        console.log('✅ 封面模态框事件监听器已绑定');
    }
}

// 设置所有表情选择器
function setupEmojiPickers() {
    const postEmojiBtn = document.getElementById('post-emoji-btn');
    const postEmojiPicker = document.getElementById('post-emoji-picker');
    const postContent = document.getElementById('post-content');
    // 只处理朋友圈发布表情
    if (postEmojiBtn && postEmojiPicker) {
        postEmojiBtn.addEventListener('click', function(e) {
            // 使用统一的表情选择器控制函数
            toggleEmojiPicker('post-emoji-picker', e);
        });
    }
    if (postEmojiPicker && postContent) {
        // 先解绑所有emoji
        const emojis = postEmojiPicker.querySelectorAll('.emoji');
        emojis.forEach(emoji => {
            emoji.replaceWith(emoji.cloneNode(true));
        });
        // 重新获取所有emoji并绑定事件
        const newEmojis = postEmojiPicker.querySelectorAll('.emoji');
        newEmojis.forEach(emoji => {
            emoji.addEventListener('click', function(e) {
                e.stopPropagation();
                const emojiChar = this.textContent;
                
                // 确保输入框获得焦点
                postContent.focus();
                
                // 获取当前光标位置
                const startPos = postContent.selectionStart || 0;
                const endPos = postContent.selectionEnd || 0;
                const textBefore = postContent.value.substring(0, startPos);
                const textAfter = postContent.value.substring(endPos);
                
                // 插入表情
                postContent.value = textBefore + emojiChar + textAfter;
                
                // 设置光标位置到表情后面
                const newCursorPos = startPos + emojiChar.length;
                postContent.setSelectionRange(newCursorPos, newCursorPos);
                
                // 隐藏表情选择器
                postEmojiPicker.classList.remove('active');
                
                // 触发input事件以便其他监听器能响应
                postContent.dispatchEvent(new Event('input', { bubbles: true }));
            });
        });
    }
    // 点击其他地方关闭表情选择器 - 修复点击外部区域隐藏逻辑
    document.addEventListener('click', function(e) {
        // 关闭发布表情选择器
        if (postEmojiPicker && postEmojiBtn && 
            !postEmojiBtn.contains(e.target) && 
            !postEmojiPicker.contains(e.target) &&
            postEmojiPicker.classList.contains('active')) {
            postEmojiPicker.classList.remove('active');
        }
        
        // 关闭朋友圈评论弹窗的表情选择器
        const commentModal = document.getElementById('moment-comment-modal');
        if (commentModal && commentModal.style.display === 'flex') {
            const commentEmojiPicker = commentModal.querySelector('#moment-comment-emoji-picker');
            const commentEmojiBtn = commentModal.querySelector('#moment-comment-emoji-btn');
            
            if (commentEmojiPicker && commentEmojiBtn && 
                !commentEmojiBtn.contains(e.target) && 
                !commentEmojiPicker.contains(e.target) &&
                commentEmojiPicker.classList.contains('active')) {
                commentEmojiPicker.classList.remove('active');
            }
        }
    });
}

// 更新用户信息显示
function updateUserInfo(targetUser = null) {
    const userName = document.getElementById('user-name');
    const userAvatar = document.getElementById('user-avatar');

    // 如果没有指定目标用户，使用当前登录用户
    let displayUser;
    if (targetUser) {
        displayUser = targetUser;
    } else {
        displayUser = JSON.parse(localStorage.getItem('wechat_user') || '{}');
    }

    if (userName) {
        userName.textContent = displayUser.username || displayUser.name || '用户名';
    }

    if (userAvatar) {
        const avatarUrl = displayUser.avatar || 'https://picsum.photos/seed/user/100/100';
        userAvatar.style.backgroundImage = `url(${avatarUrl})`;

        // 添加点击事件，点击头像进入自己的朋友圈
        userAvatar.style.cursor = 'pointer';
        userAvatar.onclick = function() {
            console.log('点击用户头像，进入自己的朋友圈');
            const currentUser = JSON.parse(localStorage.getItem('wechat_user') || '{}');
            showUserMoments(currentUser.id, currentUser.username || currentUser.name || '我', true);
        };
    }
}

// 更新封面显示（当前登录用户）
function updateCover() {
    updateUserCover(); // 不传userId，默认获取当前用户
}

// 更新指定用户的封面显示
function updateUserCover(userId = null) {
    const coverImage = document.getElementById('cover-image');
    const coverVideoContainer = document.getElementById('cover-video-container');
    const coverVideo = document.getElementById('cover-video');

    if (!coverImage || !coverVideoContainer || !coverVideo) {
        console.error('封面元素未找到');
        return;
    }

    // 构建API URL，如果有userId则获取指定用户的背景，否则获取当前用户的背景
    const apiUrl = userId ? `/api/user/cover/${userId}` : '/api/user/cover';

    // 从后台获取用户背景设置
    fetch(apiUrl, {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            const coverData = data.data;
            if (coverData.type === 'image') {
                coverImage.style.backgroundImage = `url(${coverData.url})`;
                coverImage.style.display = 'block';
                coverVideoContainer.style.display = 'none';
            } else if (coverData.type === 'video') {
                coverVideo.src = coverData.url;
                coverVideo.play();
                coverImage.style.display = 'none';
                coverVideoContainer.style.display = 'block';
            }
        } else {
            // 使用默认背景 - 固定的静态图片
            coverImage.style.backgroundImage = 'url(https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop)';
            coverImage.style.display = 'block';
            coverVideoContainer.style.display = 'none';
        }
    })
    .catch(error => {
        if (userId) {
            console.log(`用户 ${userId} 的背景获取失败，使用默认背景`);
        } else {
            console.error('获取用户背景失败:', error);
        }
        // 使用默认背景 - 固定的静态图片
        coverImage.style.backgroundImage = 'url(https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=600&h=400&fit=crop)';
        coverImage.style.display = 'block';
        coverVideoContainer.style.display = 'none';
    });
}

// 控制背景编辑功能的启用/禁用
function toggleCoverEditability(canEdit) {
    const coverWrapper = document.getElementById('cover-wrapper');
    const clickHint = coverWrapper ? coverWrapper.querySelector('.click-hint') : null;

    if (coverWrapper) {
        if (canEdit) {
            // 启用背景编辑
            coverWrapper.style.cursor = 'pointer';
            coverWrapper.onclick = showCoverModal;
            coverWrapper.title = '点击更换背景';
            // 显示点击提示
            if (clickHint) {
                clickHint.style.display = 'flex';
            }
        } else {
            // 禁用背景编辑
            coverWrapper.style.cursor = 'default';
            coverWrapper.onclick = null;
            coverWrapper.title = '';
            // 隐藏点击提示
            if (clickHint) {
                clickHint.style.display = 'none';
            }
        }
    }
}

// 渲染所有朋友圈
function renderMoments() {
    const momentsContainer = document.getElementById('moments-container');
    if (!momentsContainer) return;
    
    momentsContainer.innerHTML = '';
    
    // 按ID倒序排列，新发布的排在前面
    const sortedMoments = [...moments].sort((a, b) => b.id - a.id);
    
    sortedMoments.forEach(moment => {
        const momentElement = createMomentElement(moment);
        momentsContainer.appendChild(momentElement);
    });
}

// 创建单个朋友圈元素
function createMomentElement(moment) {
    const momentElement = document.createElement('div');
    momentElement.className = 'moment-item';
    momentElement.dataset.id = moment.id;
    
    // 头部信息（头像和用户名）
    const header = document.createElement('div');
    header.className = 'moment-header';
    
    const avatar = document.createElement('div');
    avatar.className = 'moment-avatar';
    avatar.style.backgroundImage = `url(${moment.avatar})`;
    
    const userInfo = document.createElement('div');
    
    const userName = document.createElement('div');
    userName.className = 'moment-user';
    userName.textContent = moment.user;
    
    const time = document.createElement('div');
    time.className = 'moment-time';
    time.textContent = moment.time;
    
    userInfo.appendChild(userName);
    userInfo.appendChild(time);
    
    header.appendChild(avatar);
    header.appendChild(userInfo);

    // 如果是当前用户发布的朋友圈，添加删除按钮
    if (moment.user === currentUser.name || moment.user_id === currentUser.id) {
        const deleteBtn = document.createElement('div');
        deleteBtn.className = 'moment-delete-btn';
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.title = '删除朋友圈';
        deleteBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            deleteMoment(moment.id);
        });
        header.appendChild(deleteBtn);
    }
    
    // 朋友圈内容
    const content = document.createElement('div');
    content.className = 'moment-content';
    content.textContent = moment.content;
    
    // 图片区域
    let media;
    if (moment.images && moment.images.length > 0) {
        media = document.createElement('div');
        media.className = 'moment-images';
        
        // 根据图片数量添加不同的类名
        if (moment.images.length === 1) {
            media.classList.add('single');
        } else if (moment.images.length === 2) {
            media.classList.add('dual');
        }
        
        moment.images.forEach((img, index) => {
            const imgElement = document.createElement('div');
            imgElement.className = 'moment-image';
            imgElement.style.backgroundImage = `url(${img})`;
            
            // 添加点击事件，打开图片查看器
            imgElement.addEventListener('click', () => {
                openImageViewer(moment.id, index);
            });
            
            media.appendChild(imgElement);
        });
    } else if (moment.video) {
        media = document.createElement('div');
        media.className = 'moment-video-container';
        
        const video = document.createElement('video');
        video.className = 'moment-video';
        video.src = moment.video;
        video.controls = true;
        
        // 添加点击事件，打开视频查看器
        media.addEventListener('click', (e) => {
            // 防止与视频控件冲突
            if (e.target === media) {
                openVideoViewer(moment.id);
            }
        });
        
        // 点击视频本身也应该打开查看器
        video.addEventListener('click', (e) => {
            // 阻止事件冒泡，以便控件正常工作
            e.stopPropagation();
            
            // 如果点击了控件，不打开查看器
            if (e.target === video && !video.paused) {
                openVideoViewer(moment.id);
            }
        });
        
        media.appendChild(video);
    }
    
    // 操作区域（点赞、评论）
    const actions = document.createElement('div');
    actions.className = 'moment-actions';
    
    const likeBtn = document.createElement('div');
    likeBtn.className = 'action-btn like-btn';
    
    // 检查当前用户是否已点赞
    const isLiked = moment.likes && moment.likes.includes(currentUser.name);
    if (isLiked) {
        likeBtn.classList.add('liked');
    }
    
    likeBtn.innerHTML = `<i class="fas fa-heart"></i>`;
    likeBtn.addEventListener('click', (event) => toggleLike(moment.id, event));

    const commentBtn = document.createElement('div');
    commentBtn.className = 'action-btn';
    commentBtn.innerHTML = `<i class="fas fa-comment"></i>`;
    commentBtn.addEventListener('click', () => showCommentModal(moment.id));
    
    actions.appendChild(likeBtn);
    actions.appendChild(commentBtn);
    
    // 点赞和评论区域
    const socialFeedback = document.createElement('div');
    socialFeedback.className = 'moment-social-feedback';
    
    // 点赞区域
    const likesElement = document.createElement('div');
    likesElement.className = 'moment-likes';
    
    if (moment.likes && moment.likes.length > 0) {
        likesElement.innerHTML = `<i class="fas fa-heart"></i> ${moment.likes.join('、')}`;
        socialFeedback.appendChild(likesElement);
    }
    
    // 评论区域
    const commentsElement = document.createElement('div');
    commentsElement.className = 'moment-comments';
    
    if (moment.comments && moment.comments.length > 0) {
        moment.comments.forEach(comment => {
            const commentItem = document.createElement('div');
            commentItem.className = 'comment-item';
            commentItem.dataset.id = comment.id;
            
            // 创建评论内容元素
            const commentContent = document.createElement('div');
            commentContent.className = 'comment-content';
            
            // 创建评论用户名元素
            const commentUser = document.createElement('span');
            commentUser.className = 'comment-user';
            commentUser.textContent = comment.user;
            
            commentContent.appendChild(commentUser);
            
            // 如果是回复类型的评论
            if (comment.reply_to) {
                // 添加"回复"文本
                const replyText = document.createElement('span');
                replyText.className = 'reply-text';
                replyText.textContent = ' 回复 ';
                commentContent.appendChild(replyText);
                
                // 添加被回复用户名
                if (comment.reply_to_user) {
                    const replyTarget = document.createElement('span');
                    replyTarget.className = 'reply-target';
                    replyTarget.textContent = comment.reply_to_user;
                    commentContent.appendChild(replyTarget);
                }
                
                // 添加冒号
                commentContent.appendChild(document.createTextNode('：'));
            } else {
                // 直接添加冒号
                commentContent.appendChild(document.createTextNode('：'));
            }
            
            // 添加评论正文
            const commentText = document.createElement('span');
            commentText.className = 'comment-text';
            commentText.textContent = comment.content;
            commentContent.appendChild(commentText);
            
            commentItem.appendChild(commentContent);
            
            // 添加回复按钮
            const commentActions = document.createElement('div');
            commentActions.className = 'comment-item-actions';

            const replyBtn = document.createElement('div');
            replyBtn.className = 'comment-reply-btn';
            replyBtn.innerHTML = '<i class="fas fa-reply"></i>';
            replyBtn.title = `回复 ${comment.user}`;
            // 使用数据属性存储信息，而不是直接绑定事件监听器
            replyBtn.dataset.momentId = moment.id;
            replyBtn.dataset.commentId = comment.id;
            replyBtn.dataset.userName = comment.user;

            commentActions.appendChild(replyBtn);
            commentItem.appendChild(commentActions);
            
            commentsElement.appendChild(commentItem);
        });
        
        socialFeedback.appendChild(commentsElement);
    }
    
    // 将所有元素添加到朋友圈元素中
    momentElement.appendChild(header);
    momentElement.appendChild(content);
    
    if (media) {
        momentElement.appendChild(media);
    }
    
    momentElement.appendChild(actions);
    
    // 只有当有点赞或评论时才添加社交反馈区域
    if ((moment.likes && moment.likes.length > 0) || (moment.comments && moment.comments.length > 0)) {
        momentElement.appendChild(socialFeedback);
    }
    
    return momentElement;
}

// 全局变量存储要删除的朋友圈ID
let deletingMomentId = null;

// 删除朋友圈 - 显示确认弹窗
function deleteMoment(momentId) {
    deletingMomentId = momentId;

    // 显示大厂风格确认弹窗
    const modal = document.getElementById('moments-delete-confirm-modal');
    if (modal) {
        modal.style.display = 'flex';
        // 强制重排
        modal.offsetHeight;
        setTimeout(() => {
            modal.classList.add('active');
        }, 10);
    }
}

// 隐藏朋友圈删除确认弹窗
function hideMomentsDeleteConfirmModal(immediate = false) {
    const modal = document.getElementById('moments-delete-confirm-modal');
    if (modal) {
        if (immediate) {
            // 立即关闭，用于按钮点击后
            modal.classList.add('closing');
            setTimeout(() => {
                modal.classList.remove('active', 'closing');
                modal.style.display = 'none';
            }, 250);
        } else {
            // 正常关闭动画
            modal.classList.remove('active');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 400);
        }
    }
    deletingMomentId = null;
}

// 初始化朋友圈删除确认弹窗事件监听
function initMomentsDeleteModal() {
    const modal = document.getElementById('moments-delete-confirm-modal');
    if (modal) {
        // 点击背景关闭弹窗
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideMomentsDeleteConfirmModal();
            }
        });

        // 阻止弹窗内容区域的点击事件冒泡
        const container = modal.querySelector('.moments-delete-confirm-container');
        if (container) {
            container.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // ESC键关闭弹窗
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.classList.contains('active')) {
                hideMomentsDeleteConfirmModal();
            }
        });
    }
}

// 确认删除朋友圈
function confirmDeleteMoment() {
    if (!deletingMomentId) return;

    const momentId = deletingMomentId;

    // 添加按钮点击反馈
    const confirmBtn = document.querySelector('.moments-delete-confirm-btn');
    if (confirmBtn) {
        confirmBtn.classList.add('clicked');
    }

    // 快速关闭弹窗
    hideMomentsDeleteConfirmModal(true);

    // 先尝试发送删除请求，如果失败则使用本地删除
    fetch(`/api/moments/${momentId}`, {
        method: 'DELETE',
        credentials: 'include'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('API not available');
        }
        return response.json();
    })
    .then(data => {
        if (data.code === 0) {
            // 服务器删除成功
            const index = moments.findIndex(m => m.id == momentId);
            if (index !== -1) {
                moments.splice(index, 1);
                renderMoments();
            }
            console.log('朋友圈删除成功');
            // 删除成功时不显示提示，朋友圈消失就是最好的反馈
        } else {
            showErrorToast(data.msg || '删除失败，请重试', '删除失败');
        }
    })
    .catch(error => {
        console.log('API删除失败，使用本地删除:', error);
        // API不可用时，直接进行本地删除
        const index = moments.findIndex(m => m.id == momentId);
        if (index !== -1) {
            moments.splice(index, 1);
            renderMoments();
            console.log('朋友圈本地删除成功');
            // 本地删除成功时也不显示提示
        } else {
            showErrorToast('找不到要删除的朋友圈', '删除失败');
        }
    });
}

// 取消删除朋友圈
function cancelDeleteMoment() {
    // 添加按钮点击反馈
    const cancelBtn = document.querySelector('.moments-delete-cancel-btn');
    if (cancelBtn) {
        cancelBtn.classList.add('clicked');
    }

    // 快速关闭弹窗
    hideMomentsDeleteConfirmModal(true);
}

// 打开图片查看器
function openImageViewer(momentId, imageIndex) {
    
    // 查找朋友圈
    const moment = moments.find(m => m.id === momentId);
    
    if (!moment || !moment.images || !moment.images.length) {
        return;
    }
    
    const imageViewerModal = document.getElementById('image-viewer-modal');
    const imageContainer = document.getElementById('image-container');
    
    if (!imageViewerModal || !imageContainer) {
        return;
    }
    
    // 设置当前图片查看器状态
    currentImageViewerState = {
        momentId: momentId,
        images: moment.images,
        currentIndex: imageIndex || 0
    };
    
    // 清空图片容器
    imageContainer.innerHTML = '';
    
    // 创建新图片元素
    const img = document.createElement('img');
    img.src = moment.images[currentImageViewerState.currentIndex];
    
    // 添加图片加载事件
    img.onload = function() {
        // 添加淡入动画
        setTimeout(() => {
            imageContainer.style.opacity = '1';
            imageContainer.style.transform = 'scale(1)';
        }, 50);
    };
    
    img.onerror = function() {
        console.error('Failed to load image:', img.src);
    };
    
    // 添加图片
    imageContainer.appendChild(img);
    
    // 显示弹窗
    imageViewerModal.style.display = 'flex';
    
    // 添加active类来触发CSS过渡效果
    setTimeout(() => {
        imageViewerModal.classList.add('active');
    }, 10);
    
    // 更新导航计数器
    updateImageCounter();
    
    // 根据图片数量显示或隐藏导航按钮
    updateImageNavigation();
}

// 打开视频查看器
function openVideoViewer(momentId) {
    // 查找朋友圈
    const moment = moments.find(m => m.id === momentId);
    
    if (!moment || !moment.video) return;
    
    const videoViewerModal = document.getElementById('video-viewer-modal');
    const videoPlayer = document.getElementById('video-player');
    
    if (!videoViewerModal || !videoPlayer) return;
    
    // 设置视频源
    videoPlayer.src = moment.video;
    
    // 显示弹窗
    videoViewerModal.style.display = 'flex';
    
    // 播放视频
    videoPlayer.play();
}

// 处理图片上传
function handleImageUpload(event) {
    const files = event.target.files;
    
    if (files.length === 0) return;
    
    // 检查是否有视频，图片和视频不能同时上传
    if (uploadedMedia.video) {
        showWarningToast('不能同时上传图片和视频', '上传限制');
        return;
    }
    
    // 处理图片文件
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        
        reader.onload = function(e) {
            // 限制最多9张图片
            if (uploadedMedia.images.length >= 9) {
                showWarningToast('最多上传9张图片', '上传限制');
                return;
            }
            
            // 添加到已上传媒体
            uploadedMedia.images.push(e.target.result);
            
            // 更新预览
            updatePreview();
        };
        
        reader.readAsDataURL(file);
    }
    
    // 清空input，防止选择相同文件不触发change事件
    event.target.value = '';
}

// 处理视频上传
function handleVideoUpload(event) {
    const file = event.target.files[0];
    
    if (!file) return;
    
    // 检查是否有图片，图片和视频不能同时上传
    if (uploadedMedia.images.length > 0) {
        showWarningToast('不能同时上传图片和视频', '上传限制');
        return;
    }
    
    const reader = new FileReader();
    
    reader.onload = function(e) {
        // 设置视频
        uploadedMedia.video = e.target.result;
        
        // 更新预览
        updatePreview();
    };
    
    reader.readAsDataURL(file);
    
    // 清空input，防止选择相同文件不触发change事件
    event.target.value = '';
}

// 更新媒体预览
function updatePreview() {
    const previewContainer = document.getElementById('preview-container');
    if (!previewContainer) return;
    
    previewContainer.innerHTML = '';
    
    if (uploadedMedia.images.length > 0) {
        uploadedMedia.images.forEach((image, index) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'preview-item';
            previewItem.style.backgroundImage = `url(${image})`;
            
            const removeBtn = document.createElement('div');
            removeBtn.className = 'remove-preview';
            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
            removeBtn.addEventListener('click', () => removeImage(index));
            
            previewItem.appendChild(removeBtn);
            previewContainer.appendChild(previewItem);
        });
    } else if (uploadedMedia.video) {
        const previewItem = document.createElement('div');
        previewItem.className = 'preview-item';
        
        const video = document.createElement('video');
        video.className = 'preview-video';
        video.src = uploadedMedia.video;
        video.controls = true;
        
        const removeBtn = document.createElement('div');
        removeBtn.className = 'remove-preview';
        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
        removeBtn.addEventListener('click', removeVideo);
        
        previewItem.appendChild(video);
        previewItem.appendChild(removeBtn);
        previewContainer.appendChild(previewItem);
    }
}

// 移除图片
function removeImage(index) {
    uploadedMedia.images.splice(index, 1);
    updatePreview();
}

// 移除视频
function removeVideo() {
    uploadedMedia.video = null;
    updatePreview();
}

// 从服务器获取朋友圈数据
function fetchMoments() {
    const loadingText = document.createElement('div');
    loadingText.className = 'loading-text';
    loadingText.textContent = '加载朋友圈中...';
    
    const momentsContainer = document.getElementById('moments-container');
    if (momentsContainer) {
        momentsContainer.innerHTML = '';
        momentsContainer.appendChild(loadingText);
    }
    
    fetch('/api/moments', {
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                moments = data.data.moments;
                renderMoments();
                // 启用背景编辑功能（因为这是查看自己的朋友圈）
                toggleCoverEditability(true);
            } else {
                console.error('获取朋友圈失败:', data.msg);

                // 如果是未登录错误，清理本地登录状态并重新登录
                if (data.msg === '未登录' || data.code === 401) {
                    console.log('检测到登录状态过期，清理本地状态');
                    localStorage.removeItem('wechat_user');

                    // 显示登录模态框
                    if (typeof showLoginModal === 'function') {
                        showLoginModal();
                    }

                    if (momentsContainer) {
                        momentsContainer.innerHTML = `<div class="error-message">登录已过期，请重新登录</div>`;
                    }
                } else {
                    if (momentsContainer) {
                        momentsContainer.innerHTML = `<div class="error-message">获取朋友圈失败: ${data.msg}</div>`;
                    }
                }
            }
        })
        .catch(error => {
            console.error('获取朋友圈错误:', error);

            // 如果是网络错误，可能是认证问题
            if (error.message.includes('401') || error.message.includes('Unauthorized')) {
                console.log('检测到认证错误，清理本地状态');
                localStorage.removeItem('wechat_user');

                if (typeof showLoginModal === 'function') {
                    showLoginModal();
                }

                if (momentsContainer) {
                    momentsContainer.innerHTML = `<div class="error-message">登录已过期，请重新登录</div>`;
                }
            } else {
                if (momentsContainer) {
                    momentsContainer.innerHTML = `<div class="error-message">网络连接失败，请检查网络后重试</div>`;
                }
            }
        });
}

// 显示特定用户的朋友圈
function showUserMoments(userId, userName, isCurrentUser = false) {
    console.log(`显示用户 ${userName} (ID: ${userId}) 的朋友圈`);

    // 检查当前是否已经在朋友圈页面
    const momentsPage = document.getElementById('moments-page');
    const isOnMomentsPage = momentsPage && momentsPage.classList.contains('active');

    if (!isOnMomentsPage) {
        // 只有不在朋友圈页面时才切换页面
        if (typeof showPage === 'function') {
            showPage('moments');
        }
        // 等待页面切换完成后再加载用户朋友圈
        setTimeout(() => {
            fetchUserMoments(userId, userName, isCurrentUser);
        }, 100);
    } else {
        // 已经在朋友圈页面，直接加载用户朋友圈
        fetchUserMoments(userId, userName, isCurrentUser);
    }
}

// 获取特定用户的朋友圈数据
function fetchUserMoments(userId, userName, isCurrentUser = false) {
    const momentsContainer = document.getElementById('moments-container');
    if (!momentsContainer) return;

    // 根据是否是当前用户来控制发布框的显示
    const publishSection = document.querySelector('.post-form');
    if (publishSection) {
        if (isCurrentUser) {
            publishSection.classList.remove('hidden-for-others');
        } else {
            publishSection.classList.add('hidden-for-others');
        }
    }

    // 更新头像区域显示的用户信息和背景
    if (!isCurrentUser) {
        // 如果查看的是其他用户的朋友圈，先从后端获取真实的用户信息
        fetch(`/api/user/info/${userId}`, {
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                const targetUser = {
                    id: data.data.id,
                    username: data.data.username,
                    name: data.data.username,
                    avatar: data.data.avatar
                };
                updateUserInfo(targetUser);
            } else {
                console.error('获取用户信息失败:', data.msg);
                // 使用默认信息
                const targetUser = {
                    id: userId,
                    username: userName,
                    name: userName,
                    avatar: `https://picsum.photos/seed/${userId}/100/100`
                };
                updateUserInfo(targetUser);
            }
        })
        .catch(error => {
            console.error('获取用户信息错误:', error);
            // 使用默认信息
            const targetUser = {
                id: userId,
                username: userName,
                name: userName,
                avatar: `https://picsum.photos/seed/${userId}/100/100`
            };
            updateUserInfo(targetUser);
        });

        // 更新为该用户的背景
        updateUserCover(userId);
        // 禁用背景编辑功能
        toggleCoverEditability(false);
    } else {
        // 如果查看的是自己的朋友圈，显示自己的信息
        updateUserInfo();
        // 更新为自己的背景
        updateUserCover();
        // 启用背景编辑功能
        toggleCoverEditability(true);
    }

    // 保存当前滚动位置
    const currentScrollTop = momentsContainer.scrollTop || window.pageYOffset || document.documentElement.scrollTop;

    // 检查是否已经显示了用户朋友圈头部，避免重复渲染
    const existingHeader = momentsContainer.querySelector('.user-moments-header');
    if (existingHeader) {
        // 如果已经有头部，只需要更新标题
        const titleElement = existingHeader.querySelector('h3');
        if (titleElement) {
            titleElement.textContent = `${userName} 的朋友圈`;
        }

        // 更新用户朋友圈页面的背景
        updateUserMomentsCover(userId);

        // 清除除了头部之外的所有内容（包括之前的加载提示）
        const children = Array.from(momentsContainer.children);
        children.forEach(child => {
            if (!child.classList.contains('user-moments-header')) {
                if (child.parentNode) {
                    momentsContainer.removeChild(child);
                }
            }
        });

        // 直接跳过加载提示，立即获取数据
        // 不添加加载提示，避免重复显示
    } else {
        // 首次显示用户朋友圈，清空所有内容并添加头部
        momentsContainer.innerHTML = '';

        // 添加返回按钮和背景区域
        const backButton = document.createElement('div');
        backButton.className = 'user-moments-header';
        backButton.innerHTML = `
            <div class="user-moments-cover" id="user-moments-cover"></div>
            <div class="user-moments-content">
                <button class="back-to-all-moments-btn" onclick="backToAllMoments()">
                    <i class="fas fa-arrow-left"></i> 返回朋友圈
                </button>
                <h3>${userName} 的朋友圈</h3>
            </div>
        `;
        momentsContainer.appendChild(backButton);

        // HTML结构生成后，更新用户朋友圈页面的背景
        setTimeout(() => {
            updateUserMomentsCover(userId);
        }, 50);

        // 首次进入时也不显示加载提示，直接获取数据
        // 不添加加载提示，提供更流畅的体验
    }

    fetch(`/api/moments/user/${userId}`, {
        credentials: 'include'
    })
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                // 移除加载文本
                const loadingText = momentsContainer.querySelector('.loading-text');
                if (loadingText) {
                    momentsContainer.removeChild(loadingText);
                }

                const userMoments = data.data.moments;
                if (userMoments.length === 0) {
                    const emptyMessage = document.createElement('div');
                    emptyMessage.className = 'empty-moments-message';

                    // 根据是否是当前用户显示不同的提示文案
                    const subtitle = isCurrentUser
                        ? '快来分享你的精彩瞬间吧~'
                        : '等待 TA 分享生活的精彩瞬间吧~';

                    emptyMessage.innerHTML = `
                        <div class="empty-icon">📝</div>
                        <div class="empty-title">${userName} 还没有发布朋友圈</div>
                        <div class="empty-subtitle">${subtitle}</div>
                    `;
                    emptyMessage.style.opacity = '0';
                    emptyMessage.style.transition = 'opacity 0.3s ease-in';
                    momentsContainer.appendChild(emptyMessage);

                    // 淡入效果
                    setTimeout(() => {
                        emptyMessage.style.opacity = '1';
                    }, 10);
                } else {
                    // 渲染用户的朋友圈
                    userMoments.forEach((moment, index) => {
                        const momentElement = createMomentElement(moment);
                        momentElement.style.opacity = '0';
                        momentElement.style.transition = `opacity 0.3s ease-in`;
                        momentElement.style.transitionDelay = `${index * 0.05}s`;
                        momentsContainer.appendChild(momentElement);

                        // 淡入效果
                        setTimeout(() => {
                            momentElement.style.opacity = '1';
                        }, 10);
                    });
                }
            } else {
                console.error('获取用户朋友圈失败:', data.msg);
                const loadingText = momentsContainer.querySelector('.loading-text');
                if (loadingText) {
                    momentsContainer.removeChild(loadingText);
                }

                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.textContent = `获取 ${userName} 的朋友圈失败: ${data.msg}`;
                momentsContainer.appendChild(errorMessage);
            }
        })
        .catch(error => {
            console.error('获取用户朋友圈失败:', error);
            const loadingText = momentsContainer.querySelector('.loading-text');
            if (loadingText) {
                momentsContainer.removeChild(loadingText);
            }

            const errorMessage = document.createElement('div');
            errorMessage.className = 'error-message';
            errorMessage.textContent = '网络错误，请稍后重试';
            momentsContainer.appendChild(errorMessage);
        });
}

// 返回所有朋友圈
function backToAllMoments() {
    console.log('返回所有朋友圈');

    // 重新显示发布框（因为回到了自己的朋友圈）
    const publishSection = document.querySelector('.post-form');
    if (publishSection) {
        publishSection.classList.remove('hidden-for-others');
    }

    // 恢复自己的用户信息和背景
    updateUserInfo();
    updateUserCover();
    // 启用背景编辑功能
    toggleCoverEditability(true);

    fetchMoments();
}

// 发布朋友圈
function publishMoment() {
    const postContent = document.getElementById('post-content');
    if (!postContent) return;
    
    const content = postContent.value.trim();
    
    if (!content && uploadedMedia.images.length === 0 && !uploadedMedia.video) {
        showWarningToast('请输入内容或上传图片/视频', '内容为空');
        return;
    }
    
    // 显示加载状态
    const publishBtn = document.getElementById('publish-btn');
    if (publishBtn) {
        publishBtn.disabled = true;
        publishBtn.textContent = '发布中...';
    }
    
    // 准备要发送的数据
    const postData = {
        content: content,
        images: [],
        video: null
    };
    
    // 将图片数据添加到请求中
    if (uploadedMedia.images.length > 0) {
        postData.images = uploadedMedia.images;
    }
    
    // 将视频数据添加到请求中
    if (uploadedMedia.video) {
        postData.video = uploadedMedia.video;
    }
    
    // 发送请求到服务器
    fetch('/api/moments/publish', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(postData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            // 发布成功，将新朋友圈添加到顶部
            if (data.data) {
                moments.unshift(data.data);
                renderMoments();
            } else {
                // 如果没有返回完整数据，直接刷新朋友圈列表
                fetchMoments();
            }
            
            // 清空输入框和媒体
            postContent.value = '';
            uploadedMedia = {
                images: [],
                video: null
            };
            updatePreview();
            
            // 关闭表情选择器
            const postEmojiPicker = document.getElementById('post-emoji-picker');
            if (postEmojiPicker) {
                postEmojiPicker.classList.remove('active');
            }
            
            // 发布成功 - 不显示提示，内容已经自动显示在下方
            console.log('朋友圈发布成功');
        } else {
            // 发布失败 - 显示优雅的错误提示
            showErrorToast(data.msg || '发布失败，请重试', '发布失败');
        }
    })
    .catch(error => {
        console.error('发布朋友圈错误:', error);
        // 网络错误 - 显示优雅的错误提示
        showErrorToast('网络连接异常，请检查网络后重试', '发布失败');
    })
    .finally(() => {
        // 恢复按钮状态
        if (publishBtn) {
            publishBtn.disabled = false;
            publishBtn.textContent = '发布';
        }
    });
}

// 显示评论弹窗
function showCommentModal(momentId) {
    currentCommentMomentId = momentId;
    currentReplyCommentId = null;

    const commentModal = document.getElementById('moment-comment-modal');
    const commentContent = commentModal ? commentModal.querySelector('#moment-comment-content') : null;
    const replyInfo = commentModal ? commentModal.querySelector('#moment-reply-info') : null;

    if (!commentModal || !commentContent || !replyInfo) {
        return;
    }

    // 重置评论框
    commentContent.value = '';
    commentContent.placeholder = '评论...';
    replyInfo.style.display = 'none';

    // 关闭表情选择器
    const commentEmojiPicker = commentModal.querySelector('#moment-comment-emoji-picker');
    if (commentEmojiPicker) {
        commentEmojiPicker.classList.remove('active');
    }

    // 显示弹窗并添加active类
    commentModal.style.display = 'flex';
    setTimeout(() => {
        commentModal.classList.add('active');
        commentContent.focus();
        rebindCommentModalEvents(); // 确保元素渲染后再绑定事件
    }, 10);
}

// Helper function to safely replace event listeners
const eventListenersStore = new Map();

function replaceEventListener(element, eventType, newHandler, options) {
    if (!element) return;
    const oldHandler = eventListenersStore.get(element)?.get(eventType);
    if (oldHandler) {
        element.removeEventListener(eventType, oldHandler, options);
    }
    element.addEventListener(eventType, newHandler, options);
    if (!eventListenersStore.has(element)) {
        eventListenersStore.set(element, new Map());
    }
    eventListenersStore.get(element).set(eventType, newHandler);
}

function rebindCommentModalEvents() {
    const commentModal = document.getElementById('moment-comment-modal');
    if (!commentModal) return;
    const cancelComment = commentModal.querySelector('#moment-cancel-comment');
    const submitComment = commentModal.querySelector('#moment-submit-comment');
    const commentEmojiBtn = commentModal.querySelector('#moment-comment-emoji-btn');
    const commentEmojiPicker = commentModal.querySelector('#moment-comment-emoji-picker');
    const commentContent = commentModal.querySelector('#moment-comment-content');

    // Cancel button
    if (cancelComment) {
        replaceEventListener(cancelComment, 'click', function(e) {
            e.stopPropagation();
            hideCommentModal();
        });
    }

    // Submit button
    if (submitComment) {
        replaceEventListener(submitComment, 'click', function(e) {
            e.stopPropagation();
            submitCommentHandler();
        });
    }

    // Emoji button - 修复事件绑定
    if (commentEmojiBtn) {
        replaceEventListener(commentEmojiBtn, 'click', function(e) {
            e.stopPropagation();
            toggleCommentEmojiPicker(e);
        });
    }

    // Emoji click events - 修复表情插入逻辑
    if (commentEmojiPicker && commentContent) {
        // 先解绑所有emoji
        const emojis = commentEmojiPicker.querySelectorAll('.emoji');
        emojis.forEach(emoji => {
            emoji.replaceWith(emoji.cloneNode(true));
        });
        // 重新获取所有emoji并绑定事件
        const newEmojis = commentEmojiPicker.querySelectorAll('.emoji');
        newEmojis.forEach(emoji => {
            // 检测是否为移动设备
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) ||
                           ('ontouchstart' in window) ||
                           (navigator.maxTouchPoints > 0);

            // 为移动设备添加触摸事件，为桌面设备添加点击事件
            const eventType = isMobile ? 'touchend' : 'click';

            emoji.addEventListener(eventType, function(e) {
                e.preventDefault();
                e.stopPropagation();

                // 移动端触摸反馈
                if (isMobile) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 100);
                }

                const emojiChar = this.textContent;

                // 确保输入框获得焦点
                commentContent.focus();

                // 获取当前光标位置
                const startPos = commentContent.selectionStart || 0;
                const endPos = commentContent.selectionEnd || 0;
                const textBefore = commentContent.value.substring(0, startPos);
                const textAfter = commentContent.value.substring(endPos);

                // 插入表情
                commentContent.value = textBefore + emojiChar + textAfter;

                // 设置光标位置到表情后面
                const newCursorPos = startPos + emojiChar.length;
                commentContent.setSelectionRange(newCursorPos, newCursorPos);

                // 隐藏表情选择器
                commentEmojiPicker.classList.remove('active');

                // 触发input事件以便其他监听器能响应
                commentContent.dispatchEvent(new Event('input', { bubbles: true }));
            });

            // 为移动设备添加触摸开始事件以提供即时反馈
            if (isMobile) {
                emoji.addEventListener('touchstart', function(e) {
                    e.preventDefault();
                    this.style.transform = 'scale(1.05)';
                    this.style.background = 'linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%)';
                });

                emoji.addEventListener('touchcancel', function(e) {
                    this.style.transform = '';
                    this.style.background = '';
                });
            }
        });
    }
}

// 统一的表情选择器控制函数
function toggleEmojiPicker(pickerId, e) {
    if (e) {
        e.stopPropagation(); // 防止事件冒泡
    }
    const picker = document.getElementById(pickerId);
    if (!picker) return;

    // 使用CSS类来控制显示/隐藏，统一控制方式
    const isVisible = picker.classList.contains('active');
    if (isVisible) {
        picker.classList.remove('active');
    } else {
        picker.classList.add('active');

        // 移动端位置优化
        if (pickerId === 'moment-comment-emoji-picker') {
            optimizeEmojiPickerPosition(picker);
        }
    }
}

// 优化移动端表情选择器位置
function optimizeEmojiPickerPosition(picker) {
    const isMobile = window.innerWidth <= 768;
    if (!isMobile) return;

    // 等待下一帧确保元素已渲染
    requestAnimationFrame(() => {
        const pickerRect = picker.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;

        // 检查是否超出屏幕底部
        if (pickerRect.bottom > viewportHeight) {
            // 如果超出底部，调整位置到输入框上方
            picker.style.top = 'auto';
            picker.style.bottom = '100%';
            picker.style.marginTop = '0';
            picker.style.marginBottom = '8px';
        }

        // 检查是否超出屏幕右侧
        if (pickerRect.right > viewportWidth) {
            picker.style.right = '0';
            picker.style.left = 'auto';
        }

        // 确保表情选择器在移动端有合适的最大宽度
        if (window.innerWidth <= 480) {
            picker.style.maxWidth = '100%';
        }

        // 优化移动端滚动体验
        optimizeMobileScrolling(picker);
    });
}

// 优化移动端滚动体验
function optimizeMobileScrolling(picker) {
    const emojiContainer = picker.querySelector('.emoji-container');
    if (!emojiContainer) return;

    let isScrolling = false;
    let scrollTimeout;

    // 添加滚动开始和结束的视觉反馈
    emojiContainer.addEventListener('scroll', function() {
        if (!isScrolling) {
            isScrolling = true;
            this.style.scrollBehavior = 'auto'; // 滚动时禁用平滑滚动以提升性能
        }

        // 清除之前的超时
        clearTimeout(scrollTimeout);

        // 设置滚动结束的超时
        scrollTimeout = setTimeout(() => {
            isScrolling = false;
            this.style.scrollBehavior = 'smooth'; // 恢复平滑滚动
        }, 150);
    });

    // 添加触摸滚动优化
    let startY = 0;
    let isUserScrolling = false;

    emojiContainer.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
        isUserScrolling = true;
    }, { passive: true });

    emojiContainer.addEventListener('touchmove', function(e) {
        if (!isUserScrolling) return;

        const currentY = e.touches[0].clientY;
        const deltaY = startY - currentY;

        // 如果滚动到顶部或底部，阻止进一步滚动以避免页面滚动
        if ((this.scrollTop === 0 && deltaY < 0) ||
            (this.scrollTop >= this.scrollHeight - this.clientHeight && deltaY > 0)) {
            e.preventDefault();
        }
    }, { passive: false });

    emojiContainer.addEventListener('touchend', function() {
        isUserScrolling = false;
    }, { passive: true });
}

// 专门处理评论表情选择器的显示和隐藏（保持向后兼容）
function toggleCommentEmojiPicker(e) {
    toggleEmojiPicker('moment-comment-emoji-picker', e);
}

// 显示回复弹窗
function showReplyModal(momentId, commentId, userName) {
    currentCommentMomentId = momentId;
    currentReplyCommentId = commentId;

    const commentModal = document.getElementById('moment-comment-modal');
    const commentContent = commentModal ? commentModal.querySelector('#moment-comment-content') : null;
    const replyInfo = commentModal ? commentModal.querySelector('#moment-reply-info') : null;
    const replyUser = document.getElementById('reply-user');

    if (!commentModal || !commentContent || !replyInfo || !replyUser) {
        return;
    }

    // 重置评论框
    commentContent.value = '';
    commentContent.placeholder = `回复 ${userName}...`;
    replyUser.textContent = userName;
    replyInfo.style.display = 'block';

    // 关闭表情选择器
    const commentEmojiPicker = commentModal.querySelector('#moment-comment-emoji-picker');
    if (commentEmojiPicker) {
        commentEmojiPicker.classList.remove('active');
    }

    // 显示弹窗并添加active类（与showCommentModal保持一致）
    commentModal.style.display = 'flex';
    setTimeout(() => {
        commentModal.classList.add('active');
        commentContent.focus();
        rebindCommentModalEvents(); // 确保元素渲染后再绑定事件
    }, 10);

    // 重新绑定取消回复按钮事件
    const cancelReply = commentModal.querySelector('#moment-cancel-reply');
    if (cancelReply) {
        cancelReply.removeEventListener('click', cancelReplyHandler);
        cancelReply.addEventListener('click', cancelReplyHandler);
    }
}

// 取消回复
function cancelReplyHandler() {
    currentReplyCommentId = null;
    
    const commentModal = document.getElementById('moment-comment-modal');
    const replyInfo = commentModal ? commentModal.querySelector('#moment-reply-info') : null;
    const commentContent = commentModal ? commentModal.querySelector('#moment-comment-content') : null;
    
    if (!replyInfo || !commentContent) return;
    
    replyInfo.style.display = 'none';
    commentContent.placeholder = '评论...';
}

// 隐藏评论弹窗
function hideCommentModal() {
    const commentModal = document.getElementById('moment-comment-modal');
    if (!commentModal) return;

    // 先移除active类，触发动画
    commentModal.classList.remove('active');

    // 等待动画完成后隐藏
    setTimeout(() => {
        commentModal.style.display = 'none';
        currentCommentMomentId = null;
        currentReplyCommentId = null;

        // 清空评论内容
        const commentContent = commentModal.querySelector('#moment-comment-content');
        if (commentContent) {
            commentContent.value = '';
        }

        const replyInfo = commentModal.querySelector('#moment-reply-info');
        if (replyInfo) {
            replyInfo.style.display = 'none';
        }

        // 关闭表情选择器
        const commentEmojiPicker = commentModal.querySelector('#moment-comment-emoji-picker');
        if (commentEmojiPicker) {
            commentEmojiPicker.classList.remove('active');
        }
    }, 300);
}

// 提交评论
function submitCommentHandler() {
    const commentContent = document.getElementById('moment-comment-content');
    if (!commentContent) return;
    
    const content = commentContent.value.trim();
    
    if (!content) {
        showWarningToast('请输入评论内容', '内容为空');
        return;
    }
    
    if (!currentCommentMomentId) {
        return;
    }
    
    // 显示提交中状态
    const submitButton = document.getElementById('moment-submit-comment');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.textContent = '提交中...';
    }
    
    // 准备评论数据
    const commentData = {
        content: content,
        reply_to: currentReplyCommentId
    };
    
    // 提交评论到服务器
    fetch(`/api/moments/${currentCommentMomentId}/comment`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(commentData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0) {
            // 评论成功，更新界面
            const momentIndex = moments.findIndex(m => m.id === currentCommentMomentId);
            
            if (momentIndex !== -1) {
                // 添加新评论到评论列表
                if (!moments[momentIndex].comments) {
                    moments[momentIndex].comments = [];
                }
                
                moments[momentIndex].comments.push({
                    id: data.data.id,
                    user: data.data.user,
                    content: data.data.content,
                    reply_to: data.data.reply_to,
                    reply_to_user: data.data.reply_to_user
                });
                
                // 重新渲染
                renderMoments();
                
                // 关闭弹窗
                hideCommentModal();
                
                // 滚动到新评论位置
                setTimeout(() => {
                    const newCommentElement = document.querySelector(`.comment-item[data-id="${data.data.id}"]`);
                    if (newCommentElement) {
                        newCommentElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        newCommentElement.classList.add('highlight-comment');
                        setTimeout(() => {
                            newCommentElement.classList.remove('highlight-comment');
                        }, 2000);
                    }
                }, 300);
            }
        } else {
            showErrorToast(data.msg || '评论失败，请重试', '评论失败');
        }
    })
    .catch(error => {
        console.error('评论错误:', error);
        showErrorToast('网络连接异常，请检查网络后重试', '评论失败');
    })
    .finally(() => {
        // 恢复按钮状态
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = '发送';
        }
    });
}

// 点赞/取消点赞
function toggleLike(momentId, event) {
    // 防止默认行为可能导致的页面刷新
    event.preventDefault();
    event.stopPropagation();
    
    // 立即更新UI以提供即时反馈
    const momentElement = document.querySelector(`.moment-item[data-id="${momentId}"]`);
    if (!momentElement) return;
    
    const likeBtn = momentElement.querySelector('.like-btn');
    if (!likeBtn) return;
    
    // 临时变量保存原始状态
    const wasLiked = likeBtn.classList.contains('liked');
    
    // 获取当前朋友圈对象
    const momentIndex = moments.findIndex(m => m.id === momentId);
    if (momentIndex === -1) return;
    
    const currentMoment = moments[momentIndex];
    
    // 立即切换按钮状态和数据
    if (wasLiked) {
        // 如果已经点赞，则取消点赞
        likeBtn.classList.remove('liked');
        
        // 本地状态也同步更新
        const likeIndex = currentMoment.likes.indexOf(currentUser.name);
        if (likeIndex !== -1) {
            // 先复制一个数组避免直接修改原数组
            const newLikes = [...currentMoment.likes];
            newLikes.splice(likeIndex, 1);
            currentMoment.likes = newLikes;
            
            // 只更新点赞区域，不重新渲染整个朋友圈列表
            const likesElement = momentElement.querySelector('.moment-likes');
            if (likesElement) {
                if (currentMoment.likes.length > 0) {
                    likesElement.innerHTML = `<i class="fas fa-heart"></i> ${currentMoment.likes.join('、')}`;
                } else {
                    // 如果没有点赞了，隐藏点赞区域
                    const socialFeedback = momentElement.querySelector('.moment-social-feedback');
                    if (socialFeedback) {
                        const commentsElement = momentElement.querySelector('.moment-comments');
                        if (!commentsElement || commentsElement.children.length === 0) {
                            socialFeedback.style.display = 'none';
                        } else {
                            likesElement.style.display = 'none';
                        }
                    }
                }
            }
        }
    } else {
        // 如果未点赞，则添加点赞
        likeBtn.classList.add('liked');
        
        // 本地状态也同步更新
        if (!currentMoment.likes) currentMoment.likes = [];
        if (!currentMoment.likes.includes(currentUser.name)) {
            // 先复制一个数组避免直接修改原数组
            const newLikes = [...currentMoment.likes, currentUser.name];
            currentMoment.likes = newLikes;
            
            // 只更新点赞区域，不重新渲染整个朋友圈列表
            let likesElement = momentElement.querySelector('.moment-likes');
            const socialFeedback = momentElement.querySelector('.moment-social-feedback') || 
                                    createSocialFeedback(momentElement);
            
            if (!likesElement) {
                likesElement = document.createElement('div');
                likesElement.className = 'moment-likes';
                socialFeedback.prepend(likesElement);
            } else {
                likesElement.style.display = '';
            }
            
            likesElement.innerHTML = `<i class="fas fa-heart"></i> ${currentMoment.likes.join('、')}`;
            socialFeedback.style.display = '';
        }
    }
    
    // 发送请求到服务器
    fetch(`/api/moments/${momentId}/like`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        credentials: 'include'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('点赞请求失败');
        }
        return response.json();
    })
    .then(data => {
        if (data.code !== 0) {
            // 恢复按钮原始状态
            if (wasLiked) {
                likeBtn.classList.add('liked');
            } else {
                likeBtn.classList.remove('liked');
            }
            console.error(`点赞失败: ${data.msg}`);
        }
    })
    .catch(error => {
        // 恢复按钮原始状态
        if (wasLiked) {
            likeBtn.classList.add('liked');
        } else {
            likeBtn.classList.remove('liked');
        }
        console.error('点赞操作错误:', error);
    });
}

// 创建社交反馈区域
function createSocialFeedback(momentElement) {
    const socialFeedback = document.createElement('div');
    socialFeedback.className = 'moment-social-feedback';
    momentElement.appendChild(socialFeedback);
    return socialFeedback;
}

// 处理封面图片上传
function handleCoverImageUpload(event) {
    const file = event.target.files[0];

    if (!file) return;

    // 显示上传状态
    showUploadingStatus('正在上传图片...');

    const reader = new FileReader();

    reader.onload = function(e) {
        // 上传到后台
        fetch('/api/user/cover', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                type: 'image',
                data: e.target.result
            })
        })
        .then(response => response.json())
        .then(data => {
            hideUploadingStatus();
            if (data.code === 0) {
                // 上传成功，更新UI
                updateCover();
                hideCoverModal();
                showMessage('背景图片更新成功！', 'success');
            } else {
                showMessage(`上传失败: ${data.msg}`, 'error');
            }
        })
        .catch(error => {
            hideUploadingStatus();
            console.error('上传背景图片失败:', error);
            showMessage('上传失败，请重试', 'error');
        });
    };

    reader.readAsDataURL(file);

    // 清空input
    event.target.value = '';
}

// 处理封面视频上传
function handleCoverVideoUpload(event) {
    const file = event.target.files[0];

    if (!file) return;

    // 检查文件大小（限制为500MB）
    if (file.size > 500 * 1024 * 1024) {
        showMessage('视频文件不能超过500MB', 'error');
        event.target.value = '';
        return;
    }

    // 显示上传状态
    showUploadingStatus('正在上传视频...');

    const reader = new FileReader();

    reader.onload = function(e) {
        // 上传到后台
        fetch('/api/user/cover', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            credentials: 'include',
            body: JSON.stringify({
                type: 'video',
                data: e.target.result
            })
        })
        .then(response => response.json())
        .then(data => {
            hideUploadingStatus();
            if (data.code === 0) {
                // 上传成功，更新UI
                updateCover();
                hideCoverModal();
                showMessage('背景视频更新成功！', 'success');
            } else {
                showMessage(`上传失败: ${data.msg}`, 'error');
            }
        })
        .catch(error => {
            hideUploadingStatus();
            console.error('上传背景视频失败:', error);
            showMessage('上传失败，请重试', 'error');
        });
    };

    reader.readAsDataURL(file);

    // 清空input
    event.target.value = '';
}

// 显示封面更换弹窗
function showCoverModal() {
    const coverModal = document.getElementById('cover-modal');
    if (coverModal) {
        coverModal.style.display = 'flex';
    }
}

// 隐藏封面更换弹窗
function hideCoverModal() {
    const coverModal = document.getElementById('cover-modal');
    if (coverModal) {
        coverModal.style.display = 'none';
    }
}

// 显示上传状态
function showUploadingStatus(message) {
    // 创建或更新上传状态提示
    let uploadStatus = document.getElementById('upload-status');
    if (!uploadStatus) {
        uploadStatus = document.createElement('div');
        uploadStatus.id = 'upload-status';
        uploadStatus.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 9999;
            text-align: center;
        `;
        document.body.appendChild(uploadStatus);
    }
    uploadStatus.innerHTML = `
        <div style="margin-bottom: 10px;">
            <i class="fas fa-spinner fa-spin"></i>
        </div>
        <div>${message}</div>
    `;
    uploadStatus.style.display = 'block';
}

// 隐藏上传状态
function hideUploadingStatus() {
    const uploadStatus = document.getElementById('upload-status');
    if (uploadStatus) {
        uploadStatus.style.display = 'none';
    }
}

// 显示消息提示
function showMessage(message, type = 'info') {
    // 创建消息提示元素
    const messageEl = document.createElement('div');
    messageEl.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        padding: 12px 24px;
        border-radius: 6px;
        color: white;
        font-size: 14px;
        z-index: 9999;
        transition: all 0.3s ease;
        ${type === 'success' ? 'background: #28a745;' :
          type === 'error' ? 'background: #dc3545;' :
          'background: #17a2b8;'}
    `;
    messageEl.textContent = message;

    document.body.appendChild(messageEl);

    // 3秒后自动移除
    setTimeout(() => {
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translateX(-50%) translateY(-20px)';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 300);
    }, 3000);
}

// 初始化朋友圈下拉刷新功能
function initMomentsPullToRefresh() {
    const momentsPage = document.getElementById('moments-page');
    if (!momentsPage || !window.PullToRefresh) {
        return;
    }

    // 销毁之前的实例
    if (momentsPullToRefresh) {
        momentsPullToRefresh.destroy();
    }

    // 创建新的下拉刷新实例
    momentsPullToRefresh = new PullToRefresh(momentsPage, {
        threshold: 80,
        maxDistance: 120,
        resistance: 2.5,
        refreshText: '下拉刷新朋友圈',
        releaseText: '松开刷新朋友圈',
        loadingText: '正在刷新朋友圈...',
        completeText: '朋友圈刷新完成',
        onRefresh: () => {
            return new Promise((resolve) => {
                // 重新获取朋友圈数据
                fetch('/api/moments', {
                    credentials: 'include'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0) {
                        moments = data.data.moments;
                        renderMoments();
                        console.log('朋友圈刷新成功');
                    } else {
                        console.error('刷新朋友圈失败:', data.msg);
                    }
                    resolve();
                })
                .catch(error => {
                    console.error('刷新朋友圈错误:', error);
                    resolve();
                });
            });
        }
    });
}

// 更新用户朋友圈页面的背景
function updateUserMomentsCover(userId = null) {
    const userMomentsCover = document.getElementById('user-moments-cover');

    if (!userMomentsCover) {
        console.log('用户朋友圈背景元素未找到');
        return;
    }

    const targetUserId = userId || getCurrentUser().id;

    fetch(`/api/user/cover/${targetUserId}`, {
        method: 'GET',
        credentials: 'include'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 0 && data.data) {
            const coverData = data.data;

            if (coverData.type === 'image' && coverData.url) {
                // 显示用户的背景图片
                userMomentsCover.style.backgroundImage = `url(${coverData.url})`;
                console.log(`已更新用户朋友圈背景: ${coverData.url}`);
            } else {
                // 使用默认背景
                setDefaultUserMomentsCover();
            }
        } else {
            // 使用默认背景
            setDefaultUserMomentsCover();
        }
    })
    .catch(error => {
        console.error('获取用户朋友圈背景失败:', error);
        setDefaultUserMomentsCover();
    });

    function setDefaultUserMomentsCover() {
        userMomentsCover.style.backgroundImage = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
        console.log('使用默认用户朋友圈背景');
    }
}